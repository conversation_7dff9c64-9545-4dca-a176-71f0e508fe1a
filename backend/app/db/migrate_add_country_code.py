#!/usr/bin/env python3
"""
Migration script to add country_code column to instance_pricing table
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.db.session import SessionLocal
from sqlalchemy import text

def migrate_add_country_code():
    """Add country_code column to instance_pricing table"""
    db = SessionLocal()
    try:
        # Check if column already exists
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name='instance_pricing' AND column_name='country_code'
        """))
        
        if result.fetchone():
            print("Column 'country_code' already exists in instance_pricing table")
            return
        
        # Add the country_code column
        print("Adding country_code column to instance_pricing table...")
        db.execute(text("ALTER TABLE instance_pricing ADD COLUMN country_code VARCHAR"))
        db.commit()
        print("Successfully added country_code column")
        
    except Exception as e:
        db.rollback()
        print(f"Error adding country_code column: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    migrate_add_country_code()
