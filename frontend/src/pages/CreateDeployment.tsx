import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useDeployment } from '../contexts';
import { DeploymentCreate, ServerType, PackageConfig } from '../api/types';
import { apiClient } from '../api';
import { Button } from '@/components/ui/button';
import ProgressSteps from '@/components/ui/progress-steps';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import PackageRequestModal from '@/components/PackageRequestModal';
import { getCountryFlag, getCountryName } from '../utils/flags';

type Step = 'package' | 'server' | 'cloud' | 'region' | 'instance' | 'config' | 'support' | 'review';

const CreateDeployment: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    createDeployment,
    packages,
    cloudProviders,
    regions,
    instanceTypes,
    currentPricing,
    isLoading,
    error,
    fetchPackages,
    fetchCloudProviders,
    fetchRegions,
    fetchInstanceTypes,
    calculatePricing,
    clearError,
    clearPricing
  } = useDeployment();

  const [currentStep, setCurrentStep] = useState<Step>('package');
  const [deploymentData, setDeploymentData] = useState<Partial<DeploymentCreate>>({
    package: undefined,
    server_type: undefined,
    cloud_provider: '',
    region: '',
    instance_type: '',
    support_level: '',
    admin_subdomain: '',
  });

  const [vpsValidation, setVpsValidation] = useState<{ status: 'idle' | 'validating' | 'success' | 'error'; message?: string }>({ status: 'idle' });
  const [peripheryScript, setPeripheryScript] = useState<string | null>(null);
  const [packageConfig, setPackageConfig] = useState<PackageConfig | null>(null);
  const [showPackageRequestModal, setShowPackageRequestModal] = useState(false);

  // Helper function to extract env var name from either string or object format
  const getEnvVarName = (envVar: string | { name: string; hint?: string }): string => {
    return typeof envVar === 'string' ? envVar : envVar.name;
  };

  // Helper function to extract hint from env var object
  const getEnvVarHint = (envVar: string | { name: string; hint?: string }): string | undefined => {
    return typeof envVar === 'string' ? undefined : envVar.hint;
  };

  // Helper function to convert env var name to deployment data field name
  const envVarToFieldName = (envVar: string | { name: string; hint?: string }): keyof DeploymentCreate => {
    const envVarName = getEnvVarName(envVar);
    const mapping: Record<string, keyof DeploymentCreate> = {
      'DOMAIN': 'domain',
      'EMAIL': 'admin_email',
      'ADMIN_SUBDOMAIN': 'admin_subdomain',
      'ADMIN_USERNAME': 'admin_username',
      'ADMIN_PASSWORD': 'admin_password',
      'CROWDSEC_ENROLLMENT_KEY': 'crowdsec_enrollment_key',
      'STATIC_PAGE_SUBDOMAIN': 'static_page_subdomain',
      'MAXMIND_LICENSE_KEY': 'maxmind_license_key',
      'CLIENT_ID': 'oauth_client_id',
      'CLIENT_SECRET': 'oauth_client_secret',
      'KOMODO_HOST_IP': 'komodo_host_ip',
      'KOMODO_PASSKEY': 'komodo_passkey',
      'OPENAI_API_KEY': 'openai_api_key',
      'TRAEFIK_SUBDOMAIN': 'traefik_subdomain',
      'MIDDLEWARE_MANAGER_SUBDOMAIN': 'middleware_manager_subdomain',
      'NLWEB_SUBDOMAIN': 'nlweb_subdomain',
      'LOGS_SUBDOMAIN': 'logs_subdomain'
    };

    return mapping[envVarName] || envVarName.toLowerCase() as keyof DeploymentCreate;
  };

  // Helper function to get field label from env var name
  const getFieldLabel = (envVar: string): string => {
    const labelMapping: Record<string, string> = {
      'DOMAIN': 'Domain',
      'EMAIL': 'Admin Email',
      'ADMIN_SUBDOMAIN': 'Admin Subdomain',
      'ADMIN_USERNAME': 'Admin Username',
      'ADMIN_PASSWORD': 'Admin Password',
      'CROWDSEC_ENROLLMENT_KEY': 'CrowdSec Enrollment Key',
      'STATIC_PAGE_SUBDOMAIN': 'Static Page Subdomain',
      'MAXMIND_LICENSE_KEY': 'MaxMind License Key',
      'CLIENT_ID': 'OAuth Client ID',
      'CLIENT_SECRET': 'OAuth Client Secret',
      'KOMODO_HOST_IP': 'Komodo Host IP',
      'KOMODO_PASSKEY': 'Komodo Passkey',
      'OPENAI_API_KEY': 'OpenAI API Key'
    };

    return labelMapping[envVar] || envVar.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Helper function to determine input type
  const getInputType = (envVar: string): string => {
    if (envVar.toLowerCase().includes('password') || envVar.toLowerCase().includes('secret') || envVar.toLowerCase().includes('key')) {
      return 'password';
    }
    if (envVar.toLowerCase().includes('email')) {
      return 'email';
    }
    return 'text';
  };

  // Helper function to get placeholder text
  const getPlaceholder = (envVar: string): string => {
    const placeholderMapping: Record<string, string> = {
      'DOMAIN': 'example.com',
      'EMAIL': '<EMAIL>',
      'ADMIN_SUBDOMAIN': getDefaultSubdomain(),
      'ADMIN_USERNAME': 'admin',
      'ADMIN_PASSWORD': 'Strong password',
      'STATIC_PAGE_SUBDOMAIN': 'www'
    };

    return placeholderMapping[envVar] || '';
  };

  // Helper function to get default subdomain based on package
  const getDefaultSubdomain = (): string => {
    switch (deploymentData.package) {
      case 'Coolify':
      case 'Coolify+':
        return 'coolify';
      case 'Pangolin':
      case 'Pangolin+':
      case 'Pangolin+AI':
        return 'admin';
      default:
        return 'admin';
    }
  };

  // Helper function to get all required environment variables from package config
  const getRequiredEnvVars = (): string[] => {
    if (!packageConfig) return [];

    const allEnvVars = new Set<string>();
    packageConfig.details.forEach(detail => {
      detail.required_env.forEach(env => allEnvVars.add(getEnvVarName(env)));
    });

    return Array.from(allEnvVars);
  };

  // Helper function to check if config step is completed
  const isConfigCompleted = (): boolean => {
    if (!packageConfig) return false;

    const requiredEnvVars = getRequiredEnvVars();
    return requiredEnvVars.every(envVarName => {
      const fieldName = envVarToFieldName(envVarName);
      const value = deploymentData[fieldName];
      return value && value.toString().trim() !== '';
    });
  };

  const steps: { id: Step; title: string; completed: boolean }[] = [
    { id: 'package', title: 'Package', completed: !!deploymentData.package },
    { id: 'server', title: 'Server', completed: !!deploymentData.server_type && (
      deploymentData.server_type === 'new' ||
      (deploymentData.server_type === 'vps' && vpsValidation.status === 'success')
    ) },
    { id: 'cloud', title: 'Cloud Provider', completed: deploymentData.server_type === 'new' ? !!deploymentData.cloud_provider : deploymentData.server_type ? true : false },
    { id: 'region', title: 'Region', completed: deploymentData.server_type === 'new' ? !!deploymentData.region : deploymentData.server_type ? true : false },
    { id: 'instance', title: 'Instance Type', completed: deploymentData.server_type === 'new' ? !!deploymentData.instance_type : deploymentData.server_type ? true : false },
    { id: 'config', title: 'Configuration', completed: isConfigCompleted() },
    { id: 'support', title: 'Support Level', completed: !!deploymentData.support_level },
    { id: 'review', title: 'Review', completed: false },
  ];

  useEffect(() => {
    fetchPackages();
    fetchCloudProviders();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (deploymentData.server_type === 'new' && deploymentData.cloud_provider) {
      fetchRegions(deploymentData.cloud_provider);
    }
  }, [deploymentData.server_type, deploymentData.cloud_provider]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (deploymentData.server_type === 'new' && deploymentData.cloud_provider && deploymentData.region) {
      fetchInstanceTypes(deploymentData.cloud_provider, deploymentData.region);
    }
  }, [deploymentData.server_type, deploymentData.cloud_provider, deploymentData.region]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    // Pricing: for 'new', require provider details; for 'vps'/'existing', support-only pricing available with package
    if (deploymentData.package) {
      if (deploymentData.server_type === 'new' && deploymentData.cloud_provider && deploymentData.region && deploymentData.instance_type) {
        const calculationData = {
          ...deploymentData as DeploymentCreate,
          support_level: deploymentData.support_level || 'Level 1'
        };
        calculatePricing(calculationData);
      }
      if (deploymentData.server_type && deploymentData.server_type === 'vps') {
        const calculationData = {
          ...deploymentData as DeploymentCreate,
          support_level: deploymentData.support_level || 'Level 1'
        };
        calculatePricing(calculationData);
      }
    }
  }, [
    deploymentData.package,
    deploymentData.server_type,
    deploymentData.cloud_provider,
    deploymentData.region,
    deploymentData.instance_type
  ]); // eslint-disable-line react-hooks/exhaustive-deps

  // Clear pricing when component unmounts
  useEffect(() => {
    return () => {
      clearPricing();
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  const getStepFlow = (): Step[] => {
    const flow: Step[] = ['package', 'server'];
    if (deploymentData.server_type === 'new') {
      flow.push('cloud', 'region', 'instance');
    }
    flow.push('config', 'support', 'review');
    return flow;
  };

  const handleNext = () => {
    const stepOrder = getStepFlow();
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex < stepOrder.length - 1) {
      setCurrentStep(stepOrder[currentIndex + 1]);
    }
  };

  const handleBack = () => {
    const stepOrder = getStepFlow();
    const currentIndex = stepOrder.indexOf(currentStep);
    if (currentIndex > 0) {
      // Clear pricing when navigating back to steps that affect pricing calculation
      const targetStep = stepOrder[currentIndex - 1];
      if (targetStep === 'package' || targetStep === 'cloud' || targetStep === 'region') {
        clearPricing();
      }
      setCurrentStep(targetStep);
    }
  };

  const handleSubmit = async () => {
    if (!user || !deploymentData.package) {
      return;
    }

    // Validate required fields based on server_type
    if (deploymentData.server_type === 'new') {
      if (!deploymentData.cloud_provider || !deploymentData.region || !deploymentData.instance_type) return;
    } else if (deploymentData.server_type === 'vps') {
      if (!deploymentData.vps_ip_address || vpsValidation.status !== 'success') return;
    }

    // Set default admin_subdomain for Coolify if empty
    const finalDeploymentData = { ...deploymentData };
    if (deploymentData.package === 'Coolify' && (!deploymentData.admin_subdomain || deploymentData.admin_subdomain.trim() === '')) {
      finalDeploymentData.admin_subdomain = 'coolify';
    }

    try {
      const fullDeploymentData: DeploymentCreate = {
        ...finalDeploymentData as DeploymentCreate,
      };

      const createdDeployment = await createDeployment(fullDeploymentData, user.email, user.username);
      // Navigate to dashboard with the deployment ID to auto-open details modal
      navigate(`/dashboard?deployment=${createdDeployment.id}`);
    } catch (error) {
      // Error handled by context
    }
  };

  const updateDeploymentData = (field: keyof DeploymentCreate, value: any) => {
    setDeploymentData(prev => ({ ...prev, [field]: value }));

    // Fetch package configuration when package is selected
    if (field === 'package' && value) {
      fetchPackageConfig(value);
    }

    // Reset cloud selections when switching server type
    if (field === 'server_type') {
      if (value !== 'new') {
        setDeploymentData(prev => ({
          ...prev,
          cloud_provider: undefined,
          region: undefined,
          instance_type: undefined,
        }));
      }
      clearPricing();
    }

    // Clear pricing if user changes cloud provider or region, but NOT support_level
    if (field === 'cloud_provider' || field === 'region') {
      clearPricing();
    }

    // If support level changes, recalculate pricing
    if (field === 'support_level' && deploymentData.package) {
      const calculationData = {
        ...deploymentData as DeploymentCreate,
        [field]: value
      };
      calculatePricing(calculationData);
    }
  };

  const fetchPackageConfig = async (packageName: string) => {
    try {
      const config = await apiClient.getPackageConfig(packageName);
      setPackageConfig(config);
    } catch (error) {
      console.error('Failed to fetch package config:', error);
      setPackageConfig(null);
    }
  };



  const renderStepContent = () => {
    switch (currentStep) {
      case 'package':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Choose Package</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {packages.map((pkg) => (
                  <div
                    key={pkg.name}
                    className={`relative p-6 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                      deploymentData.package === pkg.name
                        ? 'border-primary bg-primary/10 ring-2 ring-primary/20 shadow-lg'
                        : 'border-border bg-card hover:border-primary/50 hover:bg-primary/5'
                    }`}
                    onClick={() => updateDeploymentData('package', pkg.name as 'Pangolin' | 'Pangolin+' | 'Pangolin+AI' | 'Coolify' | 'Coolify+')}
                  >
                    {deploymentData.package === pkg.name && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 6L9 17l-5-5" />
                        </svg>
                      </div>
                    )}
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`w-3 h-3 rounded-full border-2 ${
                        deploymentData.package === pkg.name ? 'bg-primary border-primary' : 'border-muted-foreground'
                      }`}></div>
                      <h4 className={`text-lg font-semibold ${
                        deploymentData.package === pkg.name ? 'text-primary' : 'text-foreground'
                      }`}>
                        {pkg.display_name}
                      </h4>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {pkg.description}
                    </p>
                  </div>
                ))}

                {/* Package Request Placeholder */}
                <div
                  className="relative p-6 border-2 border-dashed border-muted-foreground/30 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary/50 hover:bg-primary/5 flex flex-col items-center justify-center min-h-[140px]"
                  onClick={() => setShowPackageRequestModal(true)}
                >
                  <div className="flex items-center justify-center w-12 h-12 rounded-full bg-muted/50 mb-3">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-muted-foreground">
                      <path d="M12 5v14" />
                      <path d="M5 12h14" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-muted-foreground mb-2">
                    Request Package
                  </h4>
                  <p className="text-sm text-muted-foreground text-center leading-relaxed">
                    Tell us what other packages you'd like to see
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'server':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Choose Server Option</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {([
                  { key: 'new', title: 'Create New Server', desc: 'Provision a new instance in your selected cloud provider.' },
                    { key: 'vps', title: 'Bring Your Own VPS', desc: 'Provide the IP address to deploy to your own server.' },
                ] as { key: ServerType; title: string; desc: string }[]).map((opt) => (
                  <div
                    key={opt.key}
                    className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                      deploymentData.server_type === opt.key 
                        ? 'border-primary bg-primary/10 ring-2 ring-primary/20 shadow-lg' 
                        : 'border-border bg-card hover:border-primary/50 hover:bg-primary/5'
                    }`}
                    onClick={() => updateDeploymentData('server_type', opt.key)}
                  >
                    {deploymentData.server_type === opt.key && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 6L9 17l-5-5" />
                        </svg>
                      </div>
                    )}
                    <div className="flex items-center gap-2 mb-2">
                      <div className={`w-3 h-3 rounded-full border-2 ${
                        deploymentData.server_type === opt.key ? 'bg-primary border-primary' : 'border-muted-foreground'
                      }`}></div>
                      <h4 className={`font-semibold ${
                        deploymentData.server_type === opt.key ? 'text-primary' : 'text-foreground'
                      }`}>
                        {opt.title}
                      </h4>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed">{opt.desc}</p>
                  </div>
                ))}
              </div>

              {deploymentData.server_type === 'new' && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-muted-foreground">Need to connect over SSH?</summary>
                  <div className="mt-2 space-y-2">
                    <Label htmlFor="user-ssh-key">SSH Public Key (Optional)</Label>
                    <textarea
                      id="user-ssh-key"
                      className="w-full p-3 border border-border rounded-md resize-none font-mono text-sm"
                      rows={3}
                      value={deploymentData.user_ssh_key || ''}
                      onChange={(e) => updateDeploymentData('user_ssh_key', e.target.value)}
                      placeholder="ssh-rsa AAAAB3NzaC1yc2EAAAADAQAB... (optional - leave empty to use system SSH keys)"
                    />
                    <p className="text-xs text-muted-foreground">
                      Provide your SSH public key to access the server. If not provided, you won't be able to SSH to the instance, but we will still be able to manage it.
                    </p>
                  </div>
                </details>
              )}


              {deploymentData.server_type === 'vps' && (
                <div className="mt-4 space-y-2">
                  <Label htmlFor="vps-ip">VPS IP Address</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="vps-ip"
                      type="text"
                      value={deploymentData.vps_ip_address || ''}
                      onChange={(e) => updateDeploymentData('vps_ip_address', e.target.value)}
                      placeholder="e.g. ************"
                    />
                    <Button
                      disabled={!deploymentData.vps_ip_address || vpsValidation.status === 'validating'}
                      onClick={async () => {
                        setVpsValidation({ status: 'validating' });
                        try {
                          const res = await apiClient.validateVps(deploymentData.vps_ip_address as string);
                          setVpsValidation({ status: 'success', message: res.message });
                        } catch (e: any) {
                          setVpsValidation({ status: 'error', message: e?.message || 'Validation failed' });
                        }
                      }}
                    >
                      {vpsValidation.status === 'validating' ? 'Validating...' : 'Validate'}
                    </Button>
                  </div>
                  {vpsValidation.status === 'error' && (
                    <p className="text-sm text-destructive">{vpsValidation.message}</p>
                  )}
                  {vpsValidation.status === 'success' && (
                    <div className="text-sm text-green-700">{vpsValidation.message}</div>
                  )}
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-muted-foreground">Need to install the periphery client?</summary>
                    <div className="mt-2 space-y-2">
                      <p className="text-sm text-muted-foreground">
                        Use this Docker Compose configuration to install the Komodo Periphery client on your VPS:
                      </p>
                      <Button
                        variant="outline"
                        onClick={async () => {
                          const res = await apiClient.getPeripheryScript(deploymentData.package);
                          setPeripheryScript(res.script);
                        }}
                      >
                        Load Docker Compose
                      </Button>
                    </div>
                    {peripheryScript && (
                      <div className="mt-2">
                        <p className="text-xs text-muted-foreground mb-2">
                          Save this as <code>docker-compose.yml</code> on your VPS and run <code>docker-compose up -d</code>
                        </p>
                        <pre className="p-2 bg-muted rounded text-xs overflow-auto max-h-64">
                          {peripheryScript}
                        </pre>
                      </div>
                    )}
                  </details>
                </div>
              )}
            </CardContent>
          </Card>
        );

      case 'cloud':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Cloud Provider</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {cloudProviders.map((provider) => (
                  <div
                    key={provider.name}
                    className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                      deploymentData.cloud_provider === provider.name
                        ? 'border-primary bg-primary/10 shadow-lg ring-2 ring-primary/20'
                        : 'border-border hover:border-primary/50 hover:bg-primary/5'
                    }`}
                    onClick={() => updateDeploymentData('cloud_provider', provider.name)}
                  >
                    {deploymentData.cloud_provider === provider.name && (
                      <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M20 6L9 17l-5-5" />
                        </svg>
                      </div>
                    )}
                    <h4 className={`font-semibold ${
                      deploymentData.cloud_provider === provider.name ? 'text-primary' : 'text-foreground'
                    }`}>
                      {provider.display_name}
                    </h4>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );

      case 'region':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Region</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {regions.length > 0 ? (
                  regions.map((region) => (
                    <div
                      key={region.name}
                      className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        deploymentData.region === region.name
                          ? 'border-primary bg-primary/10 shadow-lg ring-2 ring-primary/20'
                          : 'border-border hover:border-primary/50 hover:bg-primary/5'
                      }`}
                      onClick={() => updateDeploymentData('region', region.name)}
                    >
                      {deploymentData.region === region.name && (
                        <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 6L9 17l-5-5" />
                          </svg>
                        </div>
                      )}
                      <div className="flex items-center gap-3">
                        <div className="text-2xl" title={getCountryName(region.country_code || '')}>
                          {getCountryFlag(region.country_code || '')}
                        </div>
                        <h4 className={`font-semibold ${
                          deploymentData.region === region.name ? 'text-primary' : 'text-foreground'
                        }`}>
                          {region.display_name}
                        </h4>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground">Loading regions...</p>
                )}
              </div>
              {currentStep === 'region' && (
                <div className="mt-2 p-3 bg-primary/10 rounded-md text-sm">
                  <p className="font-medium text-primary">Region naming conventions may vary</p>
                  <p className="text-primary/80">
                    For detailed explanations and the latest availability, please consult your cloud provider’s documentation.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        );

      case 'instance':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Instance Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4">
                {instanceTypes.length > 0 ? (
                  instanceTypes.map((instance) => (
                    <div
                      key={instance.name}
                      className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                        deploymentData.instance_type === instance.name
                          ? 'border-primary bg-primary/10 shadow-lg ring-2 ring-primary/20'
                          : 'border-border hover:border-primary/50 hover:bg-primary/5'
                      }`}
                      onClick={() => updateDeploymentData('instance_type', instance.name)}
                    >
                      {deploymentData.instance_type === instance.name && (
                        <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M20 6L9 17l-5-5" />
                          </svg>
                        </div>
                      )}
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className={`font-semibold ${
                            deploymentData.instance_type === instance.name ? 'text-primary' : 'text-foreground'
                          }`}>
                            {instance.display_name}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {instance.cpu} CPU, {instance.memory}GB RAM
                          </p>
                        </div>
                        <div className="text-right">
                          <p className={`font-semibold ${
                            deploymentData.instance_type === instance.name ? 'text-primary' : 'text-foreground'
                          }`}>
                            ${instance.hourly_cost}/hr
                          </p>
                          <p className="text-sm text-muted-foreground">
                            ~${(instance.hourly_cost * 24 * 30).toFixed(0)}/month
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground">Loading instance types...</p>
                )}
              </div>
            </CardContent>
          </Card>
        );

      case 'config':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Configure Package for</CardTitle>
              {packageConfig && (
                <CardDescription>
                  {packageConfig.package_description}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {!packageConfig ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">Loading package configuration...</p>
                </div>
              ) : (
                <div className="space-y-6">
                {packageConfig.details
                  .filter((component) => component.required_env.length > 0) // only keep ones with inputs
                  .map((component, componentIndex) => (
                    <div key={component.name} className={componentIndex > 0 ? "border-t pt-6" : ""}>
                      
                      <div className="mb-4">
                        <h4 className="font-semibold text-lg">{component.name}</h4>
                        <p className="text-sm text-muted-foreground">{component.description}</p>
                      </div>

                      <div className="space-y-4">
                        {component.required_env.map((envVar) => {
                          const envVarName = getEnvVarName(envVar);
                          const envVarHint = getEnvVarHint(envVar);
                          const fieldName = envVarToFieldName(envVar);
                          const inputType = getInputType(envVarName);
                          const label = getFieldLabel(envVarName);
                          const placeholder = getPlaceholder(envVarName);
                          const fieldId = `${component.name}-${envVarName.toLowerCase()}`;

                          return (
                            <div key={envVarName}>
                              <div className="flex items-center gap-2">
                                <Label htmlFor={fieldId}>{label}</Label>
                                {envVarHint && (
                                  <div className="group relative">
                                    <button
                                      type="button"
                                      className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help touch-manipulation"
                                      aria-label="Show hint"
                                    >
                                      <svg
                                        className="h-4 w-4"
                                        fill="currentColor"
                                        viewBox="0 0 24 24"
                                      >
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                                      </svg>
                                    </button>
                                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                                      {envVarHint}
                                      <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                                    </div>
                                  </div>
                                )}
                              </div>
                              <Input
                                id={fieldId}
                                type={inputType}
                                value={(deploymentData[fieldName] as string) || ''}
                                onChange={(e) => updateDeploymentData(fieldName, e.target.value)}
                                placeholder={placeholder}
                              />
                            </div>
                          );
                        })}

                        {/* Optional Environment Variables Section */}
                        {component.optional_env && component.optional_env.length > 0 && (
                          <div className="mt-6">
                            <details className="group">
                              <summary className="flex items-center gap-2 cursor-pointer text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">
                                <svg
                                  className="h-4 w-4 transition-transform group-open:rotate-90"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                </svg>
                                Optional Settings ({component.optional_env.length})
                              </summary>
                              <div className="mt-4 space-y-4 pl-6">
                                {component.optional_env.map((envVar) => {
                                  const envVarName = getEnvVarName(envVar);
                                  const envVarHint = getEnvVarHint(envVar);
                                  const fieldName = envVarToFieldName(envVar);
                                  const inputType = getInputType(envVarName);
                                  const label = getFieldLabel(envVarName);
                                  const placeholder = getPlaceholder(envVarName);
                                  const fieldId = `${component.name}-optional-${envVarName.toLowerCase()}`;

                                  return (
                                    <div key={envVarName}>
                                      <div className="flex items-center gap-2">
                                        <Label htmlFor={fieldId} className="text-sm">{label} (Optional)</Label>
                                        {envVarHint && (
                                          <div className="group relative">
                                            <button
                                              type="button"
                                              className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help touch-manipulation"
                                              aria-label="Show hint"
                                            >
                                              <svg
                                                className="h-4 w-4"
                                                fill="currentColor"
                                                viewBox="0 0 24 24"
                                              >
                                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z"/>
                                              </svg>
                                            </button>
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                                              {envVarHint}
                                              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                                            </div>
                                          </div>
                                        )}
                                      </div>
                                      <Input
                                        id={fieldId}
                                        type={inputType}
                                        placeholder={placeholder}
                                        value={deploymentData[fieldName] || ''}
                                        onChange={(e) => updateDeploymentData(fieldName, e.target.value)}
                                        className="mt-1"
                                      />
                                    </div>
                                  );
                                })}
                              </div>
                            </details>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}

                </div>
              )}
            </CardContent>
          </Card>
        );

      case 'support':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Select Support Level</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div
                  className={`relative p-6 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    deploymentData.support_level === 'Level 1'
                      ? 'border-primary bg-primary/10 ring-2 ring-primary/20 shadow-lg'
                      : 'border-border hover:border-primary/50 hover:bg-primary/5'
                  }`}
                  onClick={() => updateDeploymentData('support_level', 'Level 1')}
                >
                  {deploymentData.support_level === 'Level 1' && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                  <h4 className={`text-xl font-semibold ${
                    deploymentData.support_level === 'Level 1' ? 'text-primary' : 'text-foreground'
                  }`}>
                    Level 1 Support
                  </h4>
                  <p className="text-sm text-muted-foreground mt-2">Included</p>
                  <ul className="mt-3 space-y-1 text-sm">
                    <li>• Access to the community forum</li>
                    <li>• Full documentation</li>
                    <li>• Basic monitoring</li>
                   </ul>
                  <p className="text-sm text-muted-foreground mt-2">Not included</p>
                  <ul className="mt-3 space-y-1 text-sm">
                    <li>• Email or direct support channels</li>
                    <li>• Guaranteed response times</li>
                    <li>• Backups or snapshots</li>
                  </ul>
                  <p className="mt-3 text-sm font-medium">Recommended for test environment</p>
                </div>

                <div
                  className={`relative p-6 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    deploymentData.support_level === 'Level 2'
                      ? 'border-primary bg-primary/10 ring-2 ring-primary/20 shadow-lg'
                      : 'border-border hover:border-primary/50 hover:bg-primary/5'
                  }`}
                  onClick={() => updateDeploymentData('support_level', 'Level 2')}
                >
                  {deploymentData.support_level === 'Level 2' && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                  <h4 className={`text-xl font-semibold ${
                    deploymentData.support_level === 'Level 2' ? 'text-primary' : 'text-foreground'
                  }`}>
                    Level 2 Support
                  </h4>
                  <p className="text-sm text-muted-foreground mt-2">$0.0685 / hour</p>
                  <ul className="mt-3 space-y-1 text-sm">
                    <li>• 7 Days of remote backup retention</li>
                    <li>• 2 Services snapshots included</li>
                    <li>• Email support channel</li>
                    <li>• 24h Response Time (business hours)</li>
                    <li>• Proactive monitoring</li>
                    <li>• Documentation & community forum</li>
                    <li>• Priority Queuing</li>
                  </ul>
                  <p className="mt-3 text-sm font-medium">Recommended for staging environment</p>
                </div>

                <div
                  className={`relative p-6 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    deploymentData.support_level === 'Level 3'
                      ? 'border-primary bg-primary/10 ring-2 ring-primary/20 shadow-lg'
                      : 'border-border hover:border-primary/50 hover:bg-primary/5'
                  }`}
                  onClick={() => updateDeploymentData('support_level', 'Level 3')}
                >
                  {deploymentData.support_level === 'Level 3' && (
                    <div className="absolute top-2 right-2 w-5 h-5 bg-primary rounded-full flex items-center justify-center">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 6L9 17l-5-5" />
                      </svg>
                    </div>
                  )}
                  <h4 className={`text-xl font-semibold ${
                    deploymentData.support_level === 'Level 3' ? 'text-primary' : 'text-foreground'
                  }`}>
                    Level 3 Support
                  </h4>
                  <p className="text-sm text-muted-foreground mt-2">$0.2740 / hour</p>
                  <ul className="mt-3 space-y-1 text-sm">
                    <li>• 30 Days of remote backup retention</li>
                    <li>• 4 Services snapshots included</li>
                    <li>• Email & Phone supports channels</li>
                    <li>• 4h Response Time (business hours)</li>
                    <li>• Proactive monitoring with email alerts</li>
                    <li>• Documentation & community forum</li>
                    <li>• Priority Queuing</li>
                    <li>• Dedicated Customer Success Manager</li>
                  </ul>
                  <p className="mt-3 text-sm font-medium">Recommended for production environment</p>
                </div>
              </div>
            </CardContent>
          </Card>
        );

      case 'review':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Review Deployment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-lg space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">Package:</span>
                  <span>{deploymentData.package}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Cloud Provider:</span>
                  <span>{deploymentData.server_type === 'vps' ? 'BYOVPS' : deploymentData.cloud_provider}</span>
                </div>
                {/* Only show region and instance type for non-BYOVPS deployments */}
                {deploymentData.server_type !== 'vps' && (
                  <>
                    <div className="flex justify-between">
                      <span className="font-medium">Region:</span>
                      <span>{deploymentData.region}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="font-medium">Instance Type:</span>
                      <span>{deploymentData.instance_type}</span>
                    </div>
                  </>
                )}
                {/* Show VPS IP for BYOVPS deployments */}
                {deploymentData.server_type === 'vps' && deploymentData.vps_ip_address && (
                  <div className="flex justify-between">
                    <span className="font-medium">VPS IP Address:</span>
                    <span>{deploymentData.vps_ip_address}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="font-medium">Domain:</span>
                  <span>{deploymentData.domain || 'Not specified'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Admin Subdomain:</span>
                  <span>{deploymentData.admin_subdomain || getDefaultSubdomain()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Support Level:</span>
                  <span>{deploymentData.support_level}</span>
                </div>

                {currentPricing && (
                  <div className="border-t pt-3">
                    <div className="flex justify-between text-lg font-semibold">
                      <span>Estimated Daily Cost:</span>
                      <span>${currentPricing.daily_cost || (currentPricing.hourly_cost * 24).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Hourly Cost:</span>
                      <span>${currentPricing.hourly_cost}</span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Monthly Cost:</span>
                      <span>${currentPricing.monthly_cost}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen">
      <div className="max-w-4xl mx-auto py-8 px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Create Deployment</h1>
          <p className="text-muted-foreground">Deploy your application to the cloud in a few simple steps</p>
        </div>

        {/* Step Progress */}
        <ProgressSteps
          steps={steps.filter(s => {
            if (s.id === 'cloud' || s.id === 'region' || s.id === 'instance') {
              return deploymentData.server_type === 'new';
            }
            return true;
          })}
          currentStepId={currentStep}
          className="mb-8"
        />

        {/* Error Display - hide \"Not Found\" errors */}
        {error && !error.toLowerCase().includes('not found') && (
          <div className="mb-6 p-4 text-destructive bg-destructive/10 border border-destructive/20 rounded">
            {error}
          </div>
        )}

        {/* Step Content */}
        <div className="mb-6">
          {renderStepContent()}
        </div>

        {/* Pricing Sidebar - show after instance type is selected OR for BYOVPS */}
        {currentPricing &&
         deploymentData.package &&
         ((deploymentData.cloud_provider && deploymentData.region && deploymentData.instance_type) ||
          deploymentData.server_type === 'vps') &&
         (currentStep === 'instance' || currentStep === 'config' || currentStep === 'support' || currentStep === 'review') && (
          <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-primary mb-2">Pricing Estimate</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Daily:</span>
                <span className="font-medium">${currentPricing.daily_cost || (currentPricing.hourly_cost * 24).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Hourly:</span>
                <span className="font-medium">${currentPricing.hourly_cost}</span>
              </div>
              <div className="flex justify-between">
                <span>Monthly:</span>
                <span className="font-medium">${currentPricing.monthly_cost}</span>
              </div>

              {/* Show breakdown for BYOVPS */}
              {currentPricing.breakdown && deploymentData.server_type === 'vps' && (
                <div className="border-t pt-2 mt-2 space-y-1 text-xs">
                  <div className="flex justify-between">
                    <span>Support:</span>
                    <span>${currentPricing.breakdown.support_cost}/hr</span>
                  </div>
                  {currentPricing.breakdown.byovps_management_fee && (
                    <div className="flex justify-between">
                      <span>BYOVPS Fee:</span>
                      <span>${currentPricing.breakdown.byovps_management_fee}/hr</span>
                    </div>
                  )}
                </div>
              )}

              {!deploymentData.support_level && currentStep === 'support' && (
                <p className="text-xs text-primary/80 mt-1">* Select a support level to see final pricing</p>
              )}
              {!deploymentData.support_level && currentStep !== 'support' && (
                <p className="text-xs text-primary/80 mt-1">* Base price with Level 1 support. Final price may change based on support level selection.</p>
              )}
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button
            variant="ghost"
            onClick={() => {
              clearPricing();
              navigate('/dashboard');
            }}
          >
            Cancel
          </Button>

          <div className="space-x-4">
            {currentStep !== 'package' && (
              <Button
                variant="outline"
                onClick={handleBack}
              >
                Back
              </Button>
            )}

            {currentStep === 'review' ? (
              <Button
                onClick={handleSubmit}
                disabled={isLoading}
              >
                {isLoading ? 'Creating...' : 'Create Deployment'}
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === 'package' && !deploymentData.package) ||
                  (currentStep === 'server' && 
                    deploymentData.server_type === 'vps' && vpsValidation.status !== 'success'
                  ) ||
                  (currentStep === 'cloud' && !deploymentData.cloud_provider) ||
                  (currentStep === 'region' && !deploymentData.region) ||
                  (currentStep === 'instance' && !deploymentData.instance_type) ||
                  (currentStep === 'config' && !isConfigCompleted())
                }
              >
                Next
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Package Request Modal */}
      <PackageRequestModal
        open={showPackageRequestModal}
        onClose={() => setShowPackageRequestModal(false)}
      />
    </div>
  );
};

export default CreateDeployment;

